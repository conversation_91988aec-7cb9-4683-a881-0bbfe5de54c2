# 送达公告模板字段说明

## 模板格式
```
${TOBACCO_BUREAU}
送达公告
烟罚送公〔${DOCUMENT_YEAR}〕第${DOCUMENT_NUMBER}号

${RECIPIENT_NAME}：

${CASE_DATE_YEAR}年${CASE_DATE_MONTH}月${CASE_DATE_DAY}日本局在${CASE_LOCATION}查获${CASE_NAME}一案，本局依据${LEGAL_BASIS}的规定作出${DECISION_TYPE}决定。根据《烟草专卖行政处罚程序规定》第五十五条第一款第四项的规定，现将${DECISION_DOCUMENT}予以公告送达。公告送达期限为30日，期限届满视为送达。




${SIGNATURE_UNIT}（印章）
${SIGNATURE_DATE_YEAR}年${SIGNATURE_DATE_MONTH}月${SIGNATURE_DATE_DAY}日
```

## 字段映射说明

| 占位符 | 字段名 | 说明 | 示例值 |
|--------|--------|------|--------|
| ${TOBACCO_BUREAU} | TOBACCO_BUREAU | 烟草专卖局名称 | 广东省博罗县烟草专卖局 |
| ${DOCUMENT_YEAR} | DOCUMENT_YEAR | 文书年份 | 2025 |
| ${DOCUMENT_NUMBER} | DOCUMENT_NUMBER | 文书编号 | 48 |
| ${RECIPIENT_NAME} | RECIPIENT_NAME | 收件人姓名 | 梁俊强 |
| ${CASE_DATE_YEAR} | CASE_DATE_YEAR | 案发年份 | 2025 |
| ${CASE_DATE_MONTH} | CASE_DATE_MONTH | 案发月份 | 03 |
| ${CASE_DATE_DAY} | CASE_DATE_DAY | 案发日期 | 18 |
| ${CASE_LOCATION} | CASE_LOCATION | 案发地点 | 广东省惠州市博罗县龙溪街道宫庭村龙桥大道1239号 |
| ${CASE_NAME} | CASE_NAME | 案件名称 | 梁俊强未在当地烟草专卖批发企业进货案 |
| ${LEGAL_BASIS} | LEGAL_BASIS | 法律依据 | 《中华人民共和国烟草专卖法实施条例》第五十六条 |
| ${DECISION_TYPE} | DECISION_TYPE | 决定类型 | 行政处罚决定书 |
| ${DECISION_DOCUMENT} | DECISION_DOCUMENT | 决定书文号 | 博烟处﹝2025﹞第48号行政处罚决定书 |
| ${SIGNATURE_UNIT} | SIGNATURE_UNIT | 落款单位 | 广东省博罗县烟草专卖局 |
| ${SIGNATURE_DATE_YEAR} | SIGNATURE_DATE_YEAR | 落款年份 | 2025 |
| ${SIGNATURE_DATE_MONTH} | SIGNATURE_DATE_MONTH | 落款月份 | 06 |
| ${SIGNATURE_DATE_DAY} | SIGNATURE_DATE_DAY | 落款日期 | 15 |

## 数据库字段映射

根据现有的数据结构，以下是从数据库字段到模板字段的映射关系：

```java
// 从数据库数据映射到模板字段
Map<String, Object> templateData = new HashMap<>();

// 基本信息
templateData.put("TOBACCO_BUREAU", data.get("DWJC")); // 单位简称
templateData.put("DOCUMENT_YEAR", data.get("ND")); // 年度
templateData.put("DOCUMENT_NUMBER", extractNumber(data.get("WSH"))); // 从文书号提取编号
templateData.put("RECIPIENT_NAME", data.get("DSR")); // 当事人

// 案发时间
templateData.put("CASE_DATE_YEAR", formatYear(data.get("AFSJ")));
templateData.put("CASE_DATE_MONTH", formatMonth(data.get("AFSJ")));
templateData.put("CASE_DATE_DAY", formatDay(data.get("AFSJ")));

// 案件信息
templateData.put("CASE_LOCATION", data.get("AFDD")); // 案发地点
templateData.put("CASE_NAME", data.get("AJMC")); // 案件名称
templateData.put("LEGAL_BASIS", data.get("CFYJ")); // 处罚依据
templateData.put("DECISION_TYPE", data.get("SDWSMC")); // 送达文书名称
templateData.put("DECISION_DOCUMENT", data.get("WSH") + data.get("SDWSMC")); // 完整文书

// 落款信息
templateData.put("SIGNATURE_UNIT", data.get("DWJC")); // 单位简称
templateData.put("SIGNATURE_DATE_YEAR", formatYear(data.get("WSRQ")));
templateData.put("SIGNATURE_DATE_MONTH", formatMonth(data.get("WSRQ")));
templateData.put("SIGNATURE_DATE_DAY", formatDay(data.get("WSRQ")));
```

## 使用方法

1. 在Word模板中使用上述占位符
2. 调用WordDocumentService.generateDocument()方法
3. 传入包含上述字段的数据Map
4. 系统会自动替换占位符并生成最终文档
